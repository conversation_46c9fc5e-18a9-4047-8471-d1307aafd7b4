# Issue #10: Rush Processing Submission - Implementation Plan

## Agent Instructions

**You are tasked with implementing missing submission trigger logic in the rush processing handler.** Be skeptical of the analysis provided and verify all claims by examining the actual codebase. The goal is to make the rush processing handler actually submit entries when appropriate, not just send alerts.

## Problem Summary

**Current Issue**: The `RequestRushProcessingHandler` promises users that rush processing will trigger submission, but it only sends backoffice alerts. Users expect their shipments to be submitted automatically when they request rush processing for eligible shipments.

**Business Impact**: Users lose trust when promised functionality doesn't work. Rush requests that should trigger immediate submission instead require manual intervention.

## Detailed Problem Analysis

### Current Handler Behavior

**File**: `apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts`

**What it does**:
1. Extracts instructions from user request
2. Sends backoffice alert via `sendBackofficeAlert()`
3. Builds compliance details for template
4. Returns response fragments

**What it doesn't do**:
1. Check if shipment is eligible for submission
2. Use proper submission workflow
3. Handle submission success/failure scenarios
4. Update template context with submission results
5. Manage database transactions properly

### Template Promises vs Reality

**Template**: `apps/portal-api/src/core-agent/templates/core-agent/fragments/document-requests/rush-processing-response.njk`

The template implies submission will happen but the handler doesn't implement it.

### Established Submission Pattern

**Reference Implementation**: `apps/portal-api/src/core-agent/handlers/process-document.handler.ts`

The codebase has an established pattern for submission that uses:
- `CustomStatusService.processShipmentForCustomsStatus()` as the submission orchestrator
- `ComplianceValidationService` for validation
- Proper database transaction management
- Structured result handling

## Implementation Strategy

### Phase 1: Add Proper Service Injection

**Follow the established pattern from `process-document.handler.ts`**:

```typescript
constructor(
  private readonly emailService: EmailService,
  private readonly customStatusService: CustomStatusService,
  private readonly complianceValidationService: ComplianceValidationService,
  private readonly dataSource: DataSource
) {
  super();
}
```

**Required Imports**:
```typescript
import { CustomStatusService } from "../../shipment/services/custom-status.service";
import { ComplianceValidationService } from "../../shipment/services/compliance-validation.service";
import { DataSource } from "typeorm";
import { Shipment } from "nest-modules";
```

### Phase 2: Implement Proper Submission Workflow

**Follow the established submission pattern**:

```typescript
// Get compliance validation first (required for submission)
const shipmentCompliances = await this.complianceValidationService.getShipmentCompliances([context.shipment]);
const validationResults = this.complianceValidationService.validateShipmentCompliances(
  shipmentCompliances,
  this.complianceValidationService.isDemoShipment(context.shipment)
);

if (validationResults.length === 0) {
  this.logger.error(`No validation results for shipment ${context.shipment.id}`);
  // Continue with alert-only behavior
  return fragments;
}

const validationResult = validationResults[0];

// Create transaction for submission (critical for data consistency)
const queryRunner = this.dataSource.createQueryRunner();
await queryRunner.connect();
await queryRunner.startTransaction();

try {
  // Use the established submission orchestrator
  const result = await this.customStatusService.processShipmentForCustomsStatus(
    context.shipment,
    validationResult,
    queryRunner
  );

  // Handle status updates (critical - sync in-memory object)
  if (result.shipmentStatusUpdate) {
    const shipmentRepository = queryRunner.manager.getRepository(Shipment);
    await shipmentRepository.update(
      { id: result.shipmentStatusUpdate.shipmentId },
      { customsStatus: result.shipmentStatusUpdate.newStatus }
    );
    context.shipment.customsStatus = result.shipmentStatusUpdate.newStatus;
  }

  await queryRunner.commitTransaction();

  // Update fragment context with structured results
  fragments[0].fragmentContext = {
    ...fragments[0].fragmentContext,
    submissionResult: result
  };

} catch (error) {
  await queryRunner.rollbackTransaction();
  this.logger.error(`Failed to process shipment for customs status: ${error.message}`);

  // Don't break the email flow - continue with alert behavior
  fragments[0].fragmentContext = {
    ...fragments[0].fragmentContext,
    submissionError: error.message
  };
} finally {
  await queryRunner.release();
}
```

### Phase 3: Update Template Context

**Template Updates for `rush-processing-response.njk`**:

```njk
{% if submissionResult %}
  {% if submissionResult.liveShipmentId %}
    <p><strong>Update:</strong> Your shipment entry has been submitted to customs as part of this rush request.</p>
  {% elif submissionResult.liveEntryUploadFailedShipment %}
    <p><strong>Note:</strong> We encountered an issue submitting your entry: {{ submissionResult.liveEntryUploadFailedShipment.failedReason }}</p>
  {% elif submissionResult.customsStatusCheckErrorShipment %}
    <p><strong>Status:</strong> {{ submissionResult.customsStatusCheckErrorShipment.errorMessage }}</p>
  {% elif submissionResult.shipmentStatusUpdate %}
    <p><strong>Status Update:</strong> Your shipment status has been updated to {{ submissionResult.shipmentStatusUpdate.newStatus }}.</p>
  {% endif %}
{% elif submissionError %}
  <p><strong>Note:</strong> We encountered an issue processing your rush request. Our team has been notified and will handle this manually.</p>
{% endif %}
```

## Critical Implementation Details

### Transaction Management (CRITICAL)

**All submission operations must be wrapped in database transactions**:
- Use `DataSource.createQueryRunner()` for transaction management
- Always commit on success, rollback on failure
- Always release the query runner in finally block
- Pass queryRunner to all database operations

### Submission Orchestration Pattern

**Use `CustomStatusService.processShipmentForCustomsStatus()` as the single source of truth**:
- This method handles all business logic for submission eligibility
- It manages the scoped `EntrySubmissionService` internally
- It returns structured results for all scenarios
- It handles timing rules, validation, and actual submission

### Result Handling

**The `processShipmentForCustomsStatus()` method returns**:
- `liveShipmentId`: Submission succeeded
- `liveEntryUploadFailedShipment`: Submission failed with reason
- `customsStatusCheckErrorShipment`: Timing/validation error
- `shipmentStatusUpdate`: Status change (may or may not include submission)

### Error Handling Strategy

**Submission Failures Should**:
1. Log the error with shipment context
2. Not break the email response flow
3. Still send the backoffice alert
4. Inform user about the specific issue encountered

### Business Logic Verification

**The submission workflow handles**:
1. Compliance validation automatically
2. Timing rules based on shipment mode and ETA dates
3. Duplicate submission prevention
4. Status updates and database consistency

## Testing Requirements

### Unit Tests Needed
- Test submission workflow with various shipment statuses
- Test transaction management (commit/rollback scenarios)
- Test result handling for all `processShipmentForCustomsStatus()` outcomes
- Test error handling when submission workflow fails
- Test that alerts still work when submission encounters issues

### Integration Tests Needed
- Test full rush processing flow with actual submission
- Verify email content reflects submission results accurately
- Test with various compliance states and timing scenarios
- Test database consistency after submission operations

### Edge Cases to Test
- Shipment already submitted (should be handled by workflow)
- Compliance issues preventing submission
- Timing restrictions preventing immediate submission
- Database transaction failures and rollback scenarios
- Missing validation data or service failures

## Skeptical Review Points

**Question these assumptions**:
1. Does the `CustomStatusService.processShipmentForCustomsStatus()` method handle all submission scenarios correctly?
2. Are database transactions properly managed in all code paths?
3. Will the template correctly display all possible submission result scenarios?
4. Does the workflow prevent duplicate submissions appropriately?

**Verify by examining**:
- The `process-document.handler.ts` implementation as the reference pattern
- `CustomStatusService.processShipmentForCustomsStatus()` method behavior
- How other handlers manage database transactions
- Template expectations for submission result display
- Existing submission workflows and their result handling

## Success Criteria

1. **Functional**: Rush requests trigger the proper submission workflow using established patterns
2. **Reliable**: Submission failures don't break the email response flow
3. **Informative**: Users receive clear feedback about submission results
4. **Safe**: Database transactions ensure data consistency and prevent corruption
5. **Maintainable**: Code follows the established `process-document.handler.ts` pattern exactly

## Implementation Checklist

- [ ] Examine `process-document.handler.ts` as the reference implementation
- [ ] Verify `CustomStatusService.processShipmentForCustomsStatus()` method behavior
- [ ] Add proper service injection following established pattern
- [ ] Implement submission workflow with database transaction management
- [ ] Handle all possible submission result scenarios
- [ ] Update template to display structured submission results
- [ ] Test submission workflow with various shipment scenarios
- [ ] Test transaction rollback on failure scenarios
- [ ] Verify no regression in existing alert functionality
- [ ] Test integration with email processing pipeline

## Expected Outcome

Users who request rush processing will have their shipments processed through the established submission workflow, with clear communication about the specific results (submission success, failure reasons, status updates, or timing restrictions). The implementation will follow established codebase patterns and maintain data consistency through proper transaction management.
