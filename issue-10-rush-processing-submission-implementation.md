# Issue #10: Rush Processing Submission - Implementation Plan

## Agent Instructions

**You are tasked with implementing missing submission trigger logic in the rush processing handler.** Be skeptical of the analysis provided and verify all claims by examining the actual codebase. The goal is to make the rush processing handler actually submit entries when appropriate, not just send alerts.

## Problem Summary

**Current Issue**: The `RequestRushProcessingHandler` promises users that rush processing will trigger submission, but it only sends backoffice alerts. Users expect their shipments to be submitted automatically when they request rush processing for eligible shipments.

**Business Impact**: Users lose trust when promised functionality doesn't work. Rush requests that should trigger immediate submission instead require manual intervention.

## Detailed Problem Analysis

### Current Handler Behavior

**File**: `apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts`

**What it does**:
1. Extracts instructions from user request
2. Sends backoffice alert via `sendBackofficeAlert()`
3. Builds compliance details for template
4. Returns response fragments

**What it doesn't do**:
1. Check if shipment is eligible for submission
2. Inject `EntrySubmissionService`
3. Call `submitShipmentEntry()` method
4. Handle submission success/failure
5. Update template context with submission results

### Template Promises vs Reality

**Template**: `apps/portal-api/src/core-agent/templates/core-agent/fragments/document-requests/rush-processing-response.njk`

The template implies submission will happen but the handler doesn't implement it.

### Service Integration Requirements

**EntrySubmissionService**: 
- Scope: `REQUEST` scoped service
- Key method: `submitShipmentEntry(shipmentOrShipmentId: Shipment | number, queryRunner?: QueryRunner)`
- Location: `apps/portal-api/src/shipment/services/entry-submission.service.ts`

## Implementation Strategy

### Phase 1: Add Service Injection

**Modify Constructor**:
```typescript
constructor(
  private readonly emailService: EmailService,
  private readonly entrySubmissionService: EntrySubmissionService
) {
  super();
}
```

**Import Required**:
```typescript
import { EntrySubmissionService } from "../../shipment/services/entry-submission.service";
```

### Phase 2: Add Submission Logic

**Business Rules to Implement**:
1. Only submit if `shipment.customsStatus === 'pending-arrival'`
2. Only submit if shipment is compliant (`isReadyToSubmit(compliance)`)
3. Handle submission success and failure scenarios
4. Update template context based on submission result

**Implementation Location**: Add to `handle()` method after backoffice alert

```typescript
// Add submission logic for eligible shipments
if (context.shipment.customsStatus === 'pending-arrival' && context.isCompliant) {
  try {
    await this.entrySubmissionService.submitShipmentEntry(context.shipment.id);
    
    // Update fragment context to reflect successful submission
    fragments[0].fragmentContext = {
      ...fragments[0].fragmentContext,
      submissionTriggered: true,
      submissionSuccess: true
    };
  } catch (error) {
    this.logger.error(`Failed to submit shipment entry for rush processing: ${error.message}`);
    
    // Update fragment context to reflect submission failure
    fragments[0].fragmentContext = {
      ...fragments[0].fragmentContext,
      submissionTriggered: true,
      submissionSuccess: false,
      submissionError: error.message
    };
  }
}
```

### Phase 3: Update Template Context

**Template Updates Needed**:
Update `rush-processing-response.njk` to handle submission results:

```njk
{% if submissionTriggered %}
  {% if submissionSuccess %}
    <p><strong>Update:</strong> Your shipment entry has been submitted to customs as part of this rush request.</p>
  {% else %}
    <p><strong>Note:</strong> We encountered an issue submitting your entry automatically. Our team has been notified and will handle this manually.</p>
  {% endif %}
{% endif %}
```

## Critical Implementation Details

### Error Handling Strategy

**Submission Failures Should**:
1. Log the error with shipment context
2. Not break the email response flow
3. Still send the backoffice alert
4. Inform user that manual processing will occur

### Service Scoping Considerations

**EntrySubmissionService is REQUEST-scoped**:
- Verify proper injection in handler constructor
- Ensure user context is available for service operations
- Check if additional scoping setup is needed

### Business Logic Verification

**Verify these conditions**:
1. `context.isCompliant` correctly identifies submittable shipments
2. `pending-arrival` is the correct status for rush submission
3. `submitShipmentEntry()` method handles the submission correctly
4. No duplicate submissions occur

## Testing Requirements

### Unit Tests Needed
- Test submission logic with eligible shipments
- Test error handling for submission failures
- Test that alerts still work when submission fails
- Test context updates for template rendering

### Integration Tests Needed
- Test full rush processing flow with submission
- Verify email content includes submission status
- Test with various shipment statuses and compliance states

### Edge Cases to Test
- Shipment already submitted
- Compliance issues preventing submission
- Service injection failures
- Database transaction failures

## Skeptical Review Points

**Question these assumptions**:
1. Is `pending-arrival` the only status that should trigger submission?
2. Should submission happen for all rush requests or only specific conditions?
3. Is the `EntrySubmissionService` the correct service to use?
4. Will this create duplicate submissions if called multiple times?

**Verify by examining**:
- Current rush processing business requirements
- Other handlers that use `EntrySubmissionService`
- Template expectations for submission behavior
- Existing submission workflows and their triggers

## Success Criteria

1. **Functional**: Rush requests for eligible shipments trigger actual submission
2. **Reliable**: Submission failures don't break the email response
3. **Informative**: Users are told whether submission occurred
4. **Safe**: No duplicate submissions or data corruption
5. **Maintainable**: Code follows existing patterns and is well-tested

## Implementation Checklist

- [ ] Examine current handler implementation thoroughly
- [ ] Verify `EntrySubmissionService` injection requirements
- [ ] Add service injection to constructor
- [ ] Implement submission logic with proper error handling
- [ ] Update template context for submission results
- [ ] Update template to display submission status
- [ ] Test submission with eligible shipments
- [ ] Test error handling scenarios
- [ ] Verify no regression in existing alert functionality
- [ ] Test integration with email processing pipeline

## Expected Outcome

Users who request rush processing for eligible shipments will have their entries automatically submitted to customs, with clear communication about whether the submission succeeded or failed. The system will fulfill its promises while maintaining reliability and proper error handling.
