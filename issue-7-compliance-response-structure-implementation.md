# Issue #7: Compliance Response Structure - Implementation Plan

## Agent Instructions

**You are tasked with fixing inconsistent compliance error formatting across email templates.** Be skeptical of the analysis provided and verify all claims by examining the actual codebase. The goal is to create a unified, structured approach to displaying compliance errors that provides comprehensive status information to users.

## Problem Summary

**Current Issue**: Compliance errors are displayed inconsistently across multiple templates with different formatting patterns, variable names, and structures. This creates confusion for users who receive different error formats depending on which email template is used.

**Business Impact**: Users receive inconsistent information about compliance issues, leading to follow-up questions and reduced trust in the system's reliability.

## Detailed Problem Analysis

### Current Inconsistencies Identified

1. **Variable Name Inconsistency**:
   - `missingFieldsFormatted` (status-message.njk)
   - `complianceErrors` (customs-status.njk)
   - `complianceDetails.formattedMissingFields` (status-line.njk)

2. **Formatting Inconsistency**:
   - Raw string display without headers
   - Different bullet point styles
   - Inconsistent HTML structure

3. **Missing Structure**:
   - No clear "Status:" sections
   - No "Validation Errors:" headers
   - Inconsistent error categorization

### Files Currently Affected

**Primary Templates**:
- `apps/portal-api/src/core-agent/templates/core-agent/fragments/status-message.njk` (lines 13, 18)
- `apps/portal-api/src/core-agent/templates/core-agent/fragments/details/status-line.njk`
- `apps/portal-api/src/core-agent/templates/core-agent/fragments/status-messages/pending-confirmation.njk`

**Secondary Templates**:
- `apps/portal-api/src/core-agent/templates/core-agent/fragments/document-requests/rush-processing-response.njk`
- `apps/portal-api/src/core-agent/templates/compliance-errors.njk`
- `apps/portal-api/src/core-agent/templates/customs-status.njk`

**Context Generation**:
- `apps/portal-api/src/agent-context/services/shipment-context.service.ts` (formatComplianceErrors method)
- `apps/portal-api/src/agent-context/services/shipment-services.adapter.ts` (formatMissingFieldsForTemplate method)

## Implementation Strategy

### Phase 1: Create Unified Compliance Error Fragment

**Create**: `apps/portal-api/src/core-agent/templates/core-agent/fragments/compliance-error-block.njk`

**Required Context Structure**:
```typescript
interface ComplianceErrorContext {
  shipment: {
    customsStatus: string;
  };
  complianceDetails: {
    formattedMissingFields: string[];
    hasMissingFields: boolean;
    missingFieldsList: string[];
  };
  formattedCustomsStatus: string;
}
```

**Template Structure**:
```njk
{% if shipment.customsStatus == 'pending-confirmation' %}
<strong>Status:</strong> Pending Confirmation - Compliance issues need to be resolved<br />
<br />

{% if complianceDetails.hasMissingFields %}
<strong>Validation Errors:</strong><br />
{% for error in complianceDetails.formattedMissingFields %}
• {{ error | replace('**missing**', '<strong>Missing</strong>') | safe }}<br />
{% endfor %}
<br />
{% endif %}
{% endif %}
```

### Phase 2: Update All Templates to Use Fragment

**Templates to Modify**:

1. **status-message.njk**: Replace inline error display with fragment include
2. **status-line.njk**: Use standardized fragment
3. **pending-confirmation.njk**: Include unified fragment
4. **rush-processing-response.njk**: Ensure consistent error display

### Phase 3: Standardize Handler Context

**Handlers to Update**:

1. **GetShipmentStatusHandler**: Ensure consistent `complianceDetails` context
2. **RequestRushProcessingHandler**: Standardize context format
3. **Other handlers**: Verify context compatibility

## Critical Implementation Details

### Context Variable Standardization

**Required in all handlers**:
```typescript
const fragmentContext = {
  shipment: context.shipment,
  complianceDetails: {
    formattedMissingFields: context.missingFieldsAnalysis.formattedMissingFields || [],
    hasMissingFields: context.missingFieldsAnalysis.formattedMissingFields?.length > 0,
    missingFieldsList: context.missingFieldsAnalysis.formattedMissingFields || []
  },
  formattedCustomsStatus: context.formattedCustomsStatus
};
```

### Data Flow Verification

**Verify this chain works correctly**:
1. `ComplianceValidationService` → detects missing fields
2. `ShipmentServicesAdapter.formatMissingFieldsForTemplate()` → formats for display
3. `ShipmentContextService` → builds template context
4. Templates → display consistently

## Testing Requirements

### Unit Tests Needed
- Test fragment with various compliance scenarios
- Verify context structure consistency
- Test HTML output formatting

### Integration Tests Needed
- Test full email processing pipeline
- Verify template rendering with real shipment data
- Test error handling scenarios

### Regression Tests Needed
- Ensure existing functionality isn't broken
- Test across different shipment statuses
- Verify backward compatibility

## Skeptical Review Points

**Question these assumptions**:
1. Are all the identified templates actually using compliance errors?
2. Is the proposed context structure compatible with existing handlers?
3. Will the unified fragment work for all use cases?
4. Are there other templates not identified that also display compliance errors?

**Verify by examining**:
- All template files for compliance error usage
- Handler implementations for context generation
- Service methods for data formatting
- Existing test cases for expected behavior

## Success Criteria

1. **Consistency**: All templates display compliance errors with identical structure
2. **Clarity**: Users see clear "Status:" and "Validation Errors:" sections
3. **Completeness**: All compliance information is displayed comprehensively
4. **Maintainability**: Single source of truth for compliance error display
5. **Backward Compatibility**: Existing functionality continues to work

## Implementation Checklist

- [ ] Examine all identified files to verify current state
- [ ] Create unified `compliance-error-block.njk` fragment
- [ ] Update `status-message.njk` to use fragment include
- [ ] Update `status-line.njk` to use fragment include
- [ ] Update `pending-confirmation.njk` to use fragment include
- [ ] Modify handlers to provide consistent context structure
- [ ] Test all compliance error scenarios
- [ ] Verify no regression in existing functionality
- [ ] Update any additional templates discovered during implementation

## Expected Outcome

Users will receive consistent, well-structured compliance error information across all email templates, reducing confusion and follow-up questions while improving trust in the system's reliability.
