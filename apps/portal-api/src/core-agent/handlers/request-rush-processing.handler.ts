import { Injectable, Inject } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent, FragmentContext } from "../types/response-fragment.types";
import { ShipmentContext, ShipmentContextWithServices } from "../../agent-context";
import { EmailService } from "../../email/services/email.service";
import { ShipmentResponseService } from "../services/shipment-response.service";

/**
 * Handles requests for expedited/rush processing of shipments.
 * Evaluates business rules and sends backoffice alerts when appropriate.
 */
@Injectable()
export class RequestRushProcessingHandler extends BaseIntentHandler {
  constructor(
    private readonly emailService: EmailService,
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_RUSH_PROCESSING" as const,
    description: "User is requesting expedited or rush processing for their shipment",
    examples: [
      "Can you rush this shipment?",
      "We need this expedited",
      "Please prioritize this load",
      "This is urgent, can you speed it up?",
      "Rush processing needed"
    ],
    keywords: ["rush", "urgent", "expedite", "prioritize", "speed", "asap", "quickly", "fast"]
  };

  async handle(
    validatedIntent: ValidatedIntent,
    context: ShipmentContextWithServices
  ): Promise<ResponseFragment[]> {
    const fragments: ResponseFragment[] = [];
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(`Handling REQUEST_RUSH_PROCESSING for shipment ${context.shipment?.id || "N/A"}`);

    if (instructions.length === 0) {
      this.logger.error("Cannot process rush processing request: no instructions provided");
      throw new Error("No instructions provided for rush processing request");
    }

    try {
      // Collect data from helper methods (no context mutation)
      const alertData = await this.sendBackofficeAlert(
        "Rush Processing Request",
        context.shipment?.id || 0,
        instructions,
        context._services.emailService,
        context.organization.id
      );
      const complianceData = this.shipmentResponseService.buildComplianceDetails(context);

      // Rush processing response fragment (priority 1)
      fragments.push({
        template: "core-agent/fragments/document-requests/rush-processing-response",
        priority: 1,
        fragmentContext: {
          ...alertData, // { backofficeAlerts: { rushProcessingSent: true } }
          ...complianceData, // { complianceDetails: {...}, missingFieldsFormatted: "..." }
          directlyAsked: { rush_processing: true }
        }
      });

      // Shipment details fragment (priority 2)
      fragments.push({
        template: "core-agent/fragments/details",
        priority: 2
      });
    } catch (error) {
      this.logger.error(
        `Failed to process rush processing request for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }

    return fragments;
  }

}
