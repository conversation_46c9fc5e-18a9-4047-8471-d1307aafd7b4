import { Injectable, Inject } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent, FragmentContext } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { CommercialInvoice, Shipment } from "nest-modules";
import { RNSStatusChangeEmailSender } from "../../shipment/senders/rns-status-change-email.sender";
import { ImporterService } from "../../importer/importer.service";
import { DataSource } from "typeorm";
import { ShipmentResponseService } from "../services/shipment-response.service";

/**
 * <PERSON>les requests for CAD (Canada Entry) documents.
 * Evaluates business rules and generates CAD documents when possible.
 */
@Injectable()
export class RequestCADDocumentHandler extends BaseIntentHandler {
  constructor(
    private readonly rnsStatusChangeEmailSender: RNSStatusChangeEmailSender,
    private readonly importerService: ImporterService,
    private readonly dataSource: DataSource,
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_CAD_DOCUMENT" as const,
    description: "User is requesting CAD (Canada Entry) document or customs entry document",
    examples: [
      "Can you send me the CAD document?",
      "I need the Canada Entry",
      "Please provide the customs entry document",
      "Send me the entry paperwork"
    ],
    keywords: ["cad", "canada entry", "customs entry", "entry document", "paperwork"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const fragments: ResponseFragment[] = [];

    this.logger.log(`Handling REQUEST_CAD_DOCUMENT for shipment ${context.shipment?.id || "N/A"}`);

    if (!context.shipment) {
      this.logger.error("Cannot generate CAD document: no shipment found in context");
      throw new Error("No shipment found for CAD document generation");
    }

    try {
      // Collect data from helper methods (no context mutation)
      const cadData = context.smartTemplateContext.cadDocumentAvailable
        ? await this.generateCADDocument(context.shipment)
        : {};

      if (!context.smartTemplateContext.cadDocumentAvailable) {
        this.logger.warn(
          `Asked to generateCADDocument when no CAD document available for shipment ${context.shipment.id}`
        );
      }

      // CAD response fragment (priority 1)
      fragments.push({
        template: "core-agent/fragments/send-cad-response",
        priority: 1,
        fragmentContext: {
          ...cadData, // { cadDocument: {...} }
          ...this.shipmentResponseService.buildComplianceDetails(context),
          directlyAsked: { cad_document: true }
        }
      });

      // Shipment details fragment (priority 2)
      fragments.push({
        template: "core-agent/fragments/details",
        priority: 2
      });
    } catch (error) {
      this.logger.error(
        `Failed to generate CAD document for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }

    return fragments;
  }

  /**
   * Generate CAD document and return as fragment context data
   */
  private async generateCADDocument(shipment: Shipment): Promise<Partial<FragmentContext>> {
    // Get commercial invoices for the shipment
    const commercialInvoices = await this.getCommercialInvoices(shipment.id);

    if (commercialInvoices.length === 0) {
      throw new Error("No commercial invoices found for this shipment");
    }

    // Get organization importer
    const importersResponse = await this.importerService.getImporters({
      organizationId: shipment.organizationId,
      limit: 1
    });
    const organizationImporter =
      importersResponse.importers.length > 0 ? importersResponse.importers[0] : null;

    // Generate CAD attachment
    const cadAttachment = await this.rnsStatusChangeEmailSender.createCADAttachment(
      shipment,
      commercialInvoices,
      organizationImporter
    );

    // Clean the base64 string
    const cleanedCadAttachment = {
      ...cadAttachment,
      b64Data: cadAttachment.b64Data.replace(/[\s\r\n\t]/g, "")
    };

    this.logger.log(`Successfully generated CAD document for shipment ${shipment.id}.`);

    return {
      cadDocument: cleanedCadAttachment
    };
  }

  /**
   * Get commercial invoices for the shipment using TypeORM
   */
  private async getCommercialInvoices(shipmentId: number): Promise<CommercialInvoice[]> {
    try {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();

      try {
        const commercialInvoices = await queryRunner.manager.getRepository(CommercialInvoice).find({
          where: { shipment: { id: shipmentId } },
          relations: {
            vendor: true,
            purchaser: true,
            countryOfExport: true,
            stateOfExport: true,
            commercialInvoiceLines: {
              origin: true,
              originState: true,
              tt: true,
              tariffCode: true
            }
          }
        });

        this.logger.log(`Found ${commercialInvoices.length} commercial invoices for shipment ${shipmentId}`);
        return commercialInvoices;
      } finally {
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(
        `Failed to get commercial invoices for shipment ${shipmentId}: ${error.message}`,
        error.stack
      );
      return [];
    }
  }

}
