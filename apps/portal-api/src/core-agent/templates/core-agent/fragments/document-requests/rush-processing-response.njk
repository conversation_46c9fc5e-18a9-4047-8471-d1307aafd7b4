{# Rush Processing Response - Consolidated Template
   Data dependencies:
   - shipment.customsStatus: Current customs status
   - smartTemplateContext.documentStatus: Document availability
   - smartTemplateContext.releaseDate: Release date if available
   - complianceDetails.missingFieldsList: Missing compliance fields
#}

{% if shipment.customsStatus == 'pending-commercial-invoice' %}
Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.

{% include "core-agent/fragments/compliance-error-block.njk" %}

{% elif shipment.customsStatus == 'pending-confirmation' %}
There are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.

{% include "core-agent/fragments/compliance-error-block.njk" %}

{% elif shipment.customsStatus in ['pending-arrival', 'live'] %}
We've received your rush request and will be submitting the entry right away.

{% elif shipment.customsStatus == 'entry-submitted' %}
The entry for the subject shipment has already been submitted. We will let you know once released by customs.

{% elif shipment.customsStatus == 'entry-accepted' %}
The subject shipment entry has already been submitted and accepted by Customs and is awaiting arrival of goods.

{% elif shipment.customsStatus == 'exam' %}
Please note the shipment has been selected for exam and will be released once the examination is complete.

{% elif shipment.customsStatus == 'released' %}
The subject shipment has been released by CBSA on {{ smartTemplateContext.releaseDate or '[release date]' }}.

{% else %}
We've received your rush request and our team will process this accordingly.

{% endif %} 