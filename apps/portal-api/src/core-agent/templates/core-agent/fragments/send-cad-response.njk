{# Consolidated Status Messages Fragment
   Data dependencies:
   - shipment.customsStatus: Current customs status
   - missingFieldsFormatted: Formatted missing fields (for pending-confirmation)
   - smartTemplateContext.hasETA: Whether ETA is available
   - smartTemplateContext.etaDate: ETA date if available
   - shipment.releaseDate: Release date if available
#}
{% if shipment.customsStatus == 'pending-commercial-invoice' %}
We can't provide the CAD yet as we're missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.

{% include "core-agent/fragments/compliance-error-block.njk" %}

{% elif shipment.customsStatus == 'pending-confirmation' %}
We're currently unable to provide the CAD as there are compliance issues or missing required fields. Please respond to the email and provide additional information for the missing fields.

{% include "core-agent/fragments/compliance-error-block.njk" %}

{% elif shipment.customsStatus == 'pending-arrival' %}
We have notified our team to send you the CAD document. Thank you for your patience.

{% elif shipment.customsStatus == 'live' %}
We have notified our team to send you the CAD document. Thank you for your patience.

{% elif shipment.customsStatus == 'entry-submitted' %}
We have notified our team to send you the CAD document. Thank you for your patience.

{% elif shipment.customsStatus == 'entry-accepted' %}
Please see CAD document attached.

{% elif shipment.customsStatus == 'exam' %}
Please see CAD document attached.

{% elif shipment.customsStatus == 'released' %}
Please see CAD document attached.

{% else %}
{# Fallback for unknown status #}
Status update: {{ shipment.customsStatus }}
We're unable to provide the CAD at this time. Please contact our support team for assistance.

{% endif %} 