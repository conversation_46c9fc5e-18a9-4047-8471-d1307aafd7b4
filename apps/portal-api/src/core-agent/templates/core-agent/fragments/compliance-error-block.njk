{# Unified compliance error display fragment — ISSUE-7 #}
{%- set status = shipment.customsStatus -%}

{# Show CI & PL: Missing for pending-commercial-invoice only #}
{%- if status == 'pending-commercial-invoice' -%}
CI & PL: Missing

{%- endif -%}

{# Show missing fields on one line if any exist #}
{%- if complianceDetails.hasMissingFields -%}
{%- for error in complianceDetails.formattedMissingFields -%}
{{ error | replace('**missing**', 'missing') | replace('**Pending**', 'Pending') }}{% if not loop.last %} {% endif %}
{%- endfor -%}

{%- endif -%}