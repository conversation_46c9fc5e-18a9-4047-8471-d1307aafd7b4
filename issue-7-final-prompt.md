# Issue #7 Final Implementation Prompt — Unified Compliance Error Display

## Objective
Eliminate inconsistent compliance‐error formatting in all portal-api email templates by introducing **one** reusable fragment and standardising the data contract that every handler passes to templates.

## Deliverables
1. **New fragment**  `apps/portal-api/src/core-agent/templates/core-agent/fragments/compliance-error-block.njk`.
2. **Template refactors** – Replace any inline compliance-error markup with `{% include "core-agent/fragments/compliance-error-block.njk" %}` in:
   - `fragments/status-message.njk`
   - `fragments/details/status-line.njk`
   - `fragments/status-messages/pending-confirmation.njk`
   - `fragments/document-requests/rush-processing-response.njk`
   - `templates/customs-status.njk`
   - and **every other** template that currently references `missingFieldsFormatted`, `complianceErrors`, or `complianceDetails.formattedMissingFields`.
3. **Context standardisation** – Every handler must supply the exact structure below (add helper in `shipment-response.service.ts` if useful):

   ```typescript
   interface ComplianceErrorContext {
     shipment: {
       customsStatus: string;
     };
     complianceDetails: {
       formattedMissingFields: string[];  // e.g. ["Port code **missing**"]
       hasMissingFields: boolean;
       missingFieldsList: string[];       // duplicate of formattedMissingFields for backward-compat
     };
     formattedCustomsStatus: string;       // already built by ShipmentServicesAdapter
   }
   ```

   Update at minimum:
   - `GetShipmentStatusHandler`
   - `RequestRushProcessingHandler`
   - any other handler that previously injected `missingFieldsFormatted`.

4. **Remove** obsolete variables (`missingFieldsFormatted`, `complianceErrors`) from handler outputs and template contexts once replacement is complete.

## Fragment Template (drop-in code)
```njk
{# Unified compliance error display fragment — ISSUE-7 #}
{% set status = shipment.customsStatus %}

{# ---- Status line ---------------------------------------------------- #}
{% if status == 'pending-confirmation' %}
<strong>Status:</strong> Pending Confirmation – Compliance issues need to be resolved<br /><br />
{% elif status == 'pending-commercial-invoice' %}
<strong>Status:</strong> Pending Commercial Invoice – Missing required documents<br /><br />
{% else %}
<strong>Status:</strong> {{ formattedCustomsStatus }}<br /><br />
{% endif %}

{# ---- Validation errors --------------------------------------------- #}
{% if complianceDetails.hasMissingFields %}
<strong>Validation Errors:</strong><br />
{% for error in complianceDetails.formattedMissingFields %}
• {{ error | replace('**missing**', '<strong>Missing</strong>') | safe }}<br />
{% endfor %}
<br />
{% endif %}
```

The fragment purposefully covers the two problematic statuses plus a fallback using `formattedCustomsStatus` to future-proof the implementation.

## Acceptance Criteria
1. **Uniform output** – All emails now show the same structure when compliance errors exist.
2. **Mandatory blocks** – A "Status:" line is always present; a "Validation Errors:" block appears only when `hasMissingFields` is true.
3. **No legacy variables** – No `.njk` references remain to `missingFieldsFormatted` or `complianceErrors`.
4. **Tests**
   - Unit tests for fragment (three scenarios: no errors, multiple errors, pending‐commercial-invoice).
   - Integration tests for `GetShipmentStatusHandler` & `RequestRushProcessingHandler` rendering.
   - All jest & lint tasks must pass: `cd apps/portal-api && rushx jest && rushx lint`.

## Implementation Checklist
- [ ] Create the fragment file with the exact content above.
- [ ] Search the codebase for `missingFieldsFormatted`, `complianceErrors`, and `complianceDetails.formattedMissingFields` → replace with include.
- [ ] Build context helper in `shipment-response.service.ts` and update handlers.
- [ ] Delete legacy variables from TS types once all usage removed.
- [ ] Add/adjust tests per Acceptance Criteria.
- [ ] Run compilation & test suite.

## Notes
* Use `<br />` for line breaks and `•` for bullet points.
* Wrap dynamic html in `| safe` after replacements.
* The fragment must fail‐safe: if any sub-property is undefined the template should still compile (Nunjucks treats undefined as falsey).
* Keep to existing coding guidelines and paths – no new dependencies required. 