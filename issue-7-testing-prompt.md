# Issue #7 QA Testing Prompt - Unified Compliance Error Display

## Context
You are a QA testing agent verifying the implementation of Issue #7: "Unified Compliance Error Display". This issue aimed to eliminate inconsistent compliance error formatting across portal-api email templates by introducing a reusable fragment.

## Expected Behavior
The system should now display compliance errors consistently across all email templates with this format:

### For pending-commercial-invoice status:
```
CI & PL: Missing

[missing fields on one line, space-separated]
```

### For pending-confirmation status:
```
[missing fields on one line, space-separated]
```

### Example Expected Outputs:
```
CI & PL: Missing

CCN missing Weight missing OGD filing Pending
```

OR

```
Weight missing Port code missing
```

## Testing Environment Setup
1. Navigate to the portal-api directory: `cd apps/portal-api`
2. Ensure the build is current: `rushx build`
3. The testing framework is already set up with comprehensive scripts

## Testing Steps

### Step 1: Find Test Shipments
Find shipments with the target statuses:

```bash
# Find pending-commercial-invoice shipments
node src/core-agent/testing/find-test-shipments.js --status=pending-commercial-invoice

# Find pending-confirmation shipments  
node src/core-agent/testing/find-test-shipments.js --status=pending-confirmation
```

**Expected**: Should return a list of shipments with IDs, HBL numbers, and recommended test intents.

### Step 2: Test REQUEST_RUSH_PROCESSING Handler
Test the rush processing handler with both statuses:

```bash
# Test with pending-commercial-invoice shipment (replace 880 with actual ID)
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_RUSH_PROCESSING --shipment=880 --no-side-effects --verbose

# Test with pending-confirmation shipment (replace 842 with actual ID)
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_RUSH_PROCESSING --shipment=842 --no-side-effects --verbose
```

**Expected Output Structure for each test**:
- ✅ Handler should complete successfully (no errors)
- 📊 Should generate 2 fragments:
  1. `core-agent/fragments/document-requests/rush-processing-response` (Priority 1)
  2. `core-agent/fragments/details` (Priority 2)
- 📧 Response preview should show the unified compliance error format

### Step 3: Test GET_SHIPMENT_STATUS Handler
Test the shipment status handler:

```bash
# Test with pending-commercial-invoice shipment
node src/core-agent/testing/test-intent-handlers.js --intent=GET_SHIPMENT_STATUS --shipment=880 --no-side-effects --verbose

# Test with pending-confirmation shipment
node src/core-agent/testing/test-intent-handlers.js --intent=GET_SHIPMENT_STATUS --shipment=842 --no-side-effects --verbose
```

**Expected Output Structure**:
- ✅ Handler should complete successfully
- 📊 Should generate 3 fragments:
  1. Specific answer template (Priority 1)
  2. `core-agent/fragments/status-message` (Priority 10)
  3. `core-agent/fragments/details` (Priority 11)
- 📧 Response should include unified compliance error format

### Step 4: Test REQUEST_CAD_DOCUMENT Handler
Test the CAD document request handler:

```bash
# Test with pending-commercial-invoice shipment
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_CAD_DOCUMENT --shipment=880 --no-side-effects --verbose

# Test with pending-confirmation shipment
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_CAD_DOCUMENT --shipment=842 --no-side-effects --verbose
```

**Expected Output Structure**:
- ✅ Handler should complete successfully
- 📊 Should generate 2 fragments:
  1. `core-agent/fragments/send-cad-response` (Priority 1)
  2. `core-agent/fragments/details` (Priority 2)
- 📧 Response should include unified compliance error format

### Step 5: Verify Fragment Context
Check that handlers provide standardized context structure:

Look for in the verbose output:
- `Context: missingFieldsFormatted, complianceDetails`
- `complianceDetails` should contain:
  - `hasMissingFields: boolean`
  - `formattedMissingFields: string[]`
  - `missingFieldsList: string[]`

### Step 6: Template Compilation Check
Verify templates compile without errors:

```bash
rushx build
```

**Expected**: Build should complete successfully with no template compilation errors.

## Verification Checklist

### ✅ Core Functionality
- [ ] All test commands execute without errors
- [ ] Handlers generate expected fragment counts
- [ ] Response previews show unified error format
- [ ] Build compiles successfully

### ✅ Format Consistency
- [ ] `pending-commercial-invoice` shows "CI & PL: Missing" + missing fields
- [ ] `pending-confirmation` shows missing fields only (no "CI & PL: Missing")
- [ ] Missing fields appear on one line, space-separated
- [ ] Format matches exactly: "CCN missing Weight missing OGD filing Pending"

### ✅ Handler Integration
- [ ] `REQUEST_RUSH_PROCESSING` uses new fragment
- [ ] `GET_SHIPMENT_STATUS` uses new fragment
- [ ] `REQUEST_CAD_DOCUMENT` uses new fragment
- [ ] All handlers provide `complianceDetails` context

### ✅ Template Structure
- [ ] No template compilation errors
- [ ] Fragment includes work correctly
- [ ] Context variables are properly passed

## Expected Success Criteria

1. **Zero Errors**: All test commands should complete without exceptions
2. **Consistent Format**: All handlers should produce the same compliance error format
3. **Proper Context**: All handlers should provide standardized `complianceDetails` context
4. **Fragment Generation**: Expected fragment counts and priorities should be maintained

## Failure Indicators

### 🚨 Red Flags
- Template compilation errors
- Handler exceptions or failures
- Missing or incorrect fragment generation
- Inconsistent error formatting between handlers
- Missing `complianceDetails` context

### 🔍 Investigation Required
- Fragment counts don't match expected values
- Response previews show old inconsistent formatting
- Context variables are missing or malformed

## Reporting Format

Please provide a summary in this format:

```
## Issue #7 QA Test Results

### Environment
- Build Status: ✅ PASS / ❌ FAIL
- Test Date: [timestamp]

### Handler Testing Results
- REQUEST_RUSH_PROCESSING: ✅ PASS / ❌ FAIL
- GET_SHIPMENT_STATUS: ✅ PASS / ❌ FAIL  
- REQUEST_CAD_DOCUMENT: ✅ PASS / ❌ FAIL

### Format Verification
- pending-commercial-invoice format: ✅ PASS / ❌ FAIL
- pending-confirmation format: ✅ PASS / ❌ FAIL
- Missing fields format: ✅ PASS / ❌ FAIL

### Fragment Context
- complianceDetails present: ✅ PASS / ❌ FAIL
- Context structure correct: ✅ PASS / ❌ FAIL

### Overall Result: ✅ PASS / ❌ FAIL

### Issues Found
[List any issues or concerns]

### Sample Output
[Include actual response preview showing the unified format]
```

## Notes
- Use `--no-side-effects` flag to prevent actual email sending
- Focus on the response preview format, not the full email content
- All shipment IDs should be from organization 3 (demo org)
- If you encounter template errors, that's a critical failure
- The format should be exactly as specified in the Expected Behavior section