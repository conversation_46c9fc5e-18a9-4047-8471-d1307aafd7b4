# Issue #11: CAD Template Alignment - Implementation Plan

## Agent Instructions

**You are tasked with aligning CAD template logic with business rules to eliminate false promises.** Be skeptical of the analysis provided and verify all claims by examining the actual codebase. The goal is to ensure the CAD response template only promises CAD documents when they can actually be provided.

## Problem Summary

**Current Issue**: The `send-cad-response.njk` template promises CAD documents for shipment statuses where the business logic (`isSendCADReady()`) doesn't allow CAD generation. This creates false expectations and erodes user trust.

**Business Impact**: Users expect CAD documents that will never be delivered, leading to follow-up inquiries and reduced confidence in the system.

## Detailed Problem Analysis

### Current Template Logic vs Business Rules

**Template Promises CAD For**:
- `pending-arrival`: "We have notified our team to send you the CAD document"
- `live`: "We have notified our team to send you the CAD document"  
- `entry-submitted`: "We have notified our team to send you the CAD document"

**Business Logic Only Allows CAD For**:
```typescript
// From apps/portal-api/src/core-agent/constants/customs-definitions.constants.ts
export function isSendCADReady(status: string | null | undefined): boolean {
  return [
    CustomsStatus.ENTRY_ACCEPTED,
    CustomsStatus.EXAM,
    CustomsStatus.RELEASED,
    CustomsStatus.ACCOUNTING_COMPLETED
  ].includes(status as CustomsStatus);
}
```

### Status Mapping Issues

**CustomsStatus Enum Values**:
- `PENDING_ARRIVAL = "pending-arrival"`
- `LIVE = "live"`
- `ENTRY_SUBMITTED = "entry-submitted"`
- `ENTRY_ACCEPTED = "entry-accepted"`
- `EXAM = "exam"`
- `RELEASED = "released"`
- `ACCOUNTING_COMPLETED = "accounting-completed"`

**The Gap**: Template conditions use statuses that `isSendCADReady()` explicitly excludes.

### Missing Transaction Number Display

**Additional Issue**: Templates don't display transaction numbers when available, which is important context for users.

## Implementation Strategy

### Phase 1: Align Template with Business Logic

**File to Modify**: `apps/portal-api/src/core-agent/templates/core-agent/fragments/send-cad-response.njk`

**Current Problematic Sections**:
```njk
{% elif shipment.customsStatus == 'pending-arrival' %}
We have notified our team to send you the CAD document. Thank you for your patience.

{% elif shipment.customsStatus == 'live' %}
We have notified our team to send you the CAD document. Thank you for your patience.

{% elif shipment.customsStatus == 'entry-submitted' %}
We have notified our team to send you the CAD document. Thank you for your patience.
```

**Proposed Replacement**:
```njk
{% elif shipment.customsStatus == 'pending-arrival' %}
The CAD document will be available once the customs entry is accepted. Current status: {{ formattedCustomsStatus }}.

{% elif shipment.customsStatus == 'live' %}
The customs entry is ready for submission. The CAD document will be available once accepted by customs.

{% elif shipment.customsStatus == 'entry-submitted' %}
The customs entry has been submitted and is awaiting acceptance. The CAD document will be available once accepted.
```

### Phase 2: Add Transaction Number Display

**Context Variable to Use**: `shipment.transactionNumber` or equivalent

**Template Addition**:
```njk
{% if shipment.transactionNumber %}
<br />Transaction Number: <strong>{{ shipment.transactionNumber }}</strong>
{% endif %}
```

### Phase 3: Use Business Logic in Template

**Option 1: Add Context Variable**
Add `canGenerateCAD` to template context using `isSendCADReady()`:

```typescript
// In handler context building
const fragmentContext = {
  ...existingContext,
  canGenerateCAD: isSendCADReady(context.shipment.customsStatus)
};
```

**Option 2: Template Logic Alignment**
Ensure template conditions exactly match `isSendCADReady()` logic:

```njk
{% if shipment.customsStatus == 'entry-accepted' or 
     shipment.customsStatus == 'exam' or 
     shipment.customsStatus == 'released' or 
     shipment.customsStatus == 'accounting-completed' %}
Please see CAD document attached.
{% else %}
{# Provide appropriate status-specific messaging #}
{% endif %}
```

## Critical Implementation Details

### Handler Integration

**Verify which handlers use this template**:
- `RequestCADDocumentHandler` (primary user)
- Any other handlers that might reference CAD functionality

**Ensure handlers provide**:
- `formattedCustomsStatus` for user-friendly status display
- `shipment.transactionNumber` when available
- Consistent context structure

### Business Logic Verification

**Verify `isSendCADReady()` is correct**:
1. Check if the allowed statuses are actually when CAD is available
2. Verify no other business rules affect CAD availability
3. Confirm the function is used consistently across the codebase

### Context Variable Availability

**Verify these variables exist in template context**:
- `shipment.customsStatus`
- `shipment.transactionNumber`
- `formattedCustomsStatus`
- Any other variables used in the template

## Testing Requirements

### Unit Tests Needed
- Test template rendering for each customs status
- Verify CAD availability logic matches business rules
- Test transaction number display when available/unavailable

### Integration Tests Needed
- Test CAD request flow with various shipment statuses
- Verify email content matches actual CAD availability
- Test user experience for each status scenario

### Business Logic Tests Needed
- Verify `isSendCADReady()` returns correct values
- Test edge cases and status transitions
- Confirm no other factors affect CAD availability

## Skeptical Review Points

**Question these assumptions**:
1. Is `isSendCADReady()` the definitive source of truth for CAD availability?
2. Are there other business rules that affect when CAD can be sent?
3. Should the template provide different messaging for different unavailable statuses?
4. Is transaction number display always appropriate?

**Verify by examining**:
- All uses of `isSendCADReady()` in the codebase
- CAD generation and attachment logic
- User feedback about current CAD request experience
- Business requirements for CAD document delivery

## Success Criteria

1. **Accuracy**: Template only promises CAD when `isSendCADReady()` returns true
2. **Clarity**: Users understand when CAD will be available and why
3. **Consistency**: Template logic matches business logic exactly
4. **Informativeness**: Users see relevant status and transaction information
5. **Trust**: No false promises that erode user confidence

## Implementation Checklist

- [ ] Examine current template and business logic thoroughly
- [ ] Verify `isSendCADReady()` function behavior and usage
- [ ] Update template conditions to match business logic exactly
- [ ] Add appropriate messaging for non-CAD-ready statuses
- [ ] Implement transaction number display
- [ ] Test template with all possible customs statuses
- [ ] Verify handler context provides required variables
- [ ] Test user experience for each scenario
- [ ] Ensure no regression in existing functionality

## Expected Outcome

Users will receive accurate information about CAD document availability that matches the system's actual capabilities. The template will provide clear, status-appropriate messaging that builds trust rather than creating false expectations. Transaction numbers will be displayed when available to provide additional context.
